{"private": true, "version": "3.5.16", "packageManager": "pnpm@10.11.1", "type": "module", "scripts": {"dev": "node scripts/dev.js", "build": "node scripts/build.js", "build-dts": "tsc -p tsconfig.build.json --noCheck && rollup -c rollup.dts.config.js", "clean": "rimraf --glob packages/*/dist temp .eslintcache", "size": "run-s \"size-*\" && node scripts/usage-size.js", "size-global": "node scripts/build.js vue runtime-dom -f global -p --size", "size-esm-runtime": "node scripts/build.js vue -f esm-bundler-runtime", "size-esm": "node scripts/build.js runtime-dom runtime-core reactivity shared -f esm-bundler", "check": "tsc --incremental --noEmit", "lint": "eslint --cache .", "format": "prettier --write --cache .", "format-check": "prettier --check --cache .", "test": "vitest", "test-unit": "vitest --project unit", "test-e2e": "node scripts/build.js vue -f global -d && vitest --project e2e", "test-dts": "run-s build-dts test-dts-only", "test-dts-only": "tsc -p packages-private/dts-built-test/tsconfig.json && tsc -p ./packages-private/dts-test/tsconfig.test.json", "test-coverage": "vitest run --project unit --coverage", "prebench": "node scripts/build.js -pf esm-browser reactivity", "prebench-compare": "node scripts/build.js -pf esm-browser reactivity", "bench": "vitest bench --project=unit --outputJson=temp/bench.json", "bench-compare": "vitest bench --project=unit --compare=temp/bench.json", "release": "node scripts/release.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "dev-esm": "node scripts/dev.js -if esm-bundler-runtime", "dev-compiler": "run-p \"dev template-explorer\" serve", "dev-sfc": "run-s dev-sfc-prepare dev-sfc-run", "dev-sfc-prepare": "node scripts/pre-dev-sfc.js || npm run build-all-cjs", "dev-sfc-serve": "vite packages-private/sfc-playground --host", "dev-sfc-run": "run-p \"dev compiler-sfc -f esm-browser\" \"dev vue -if esm-bundler-runtime\" \"dev vue -ipf esm-browser-runtime\" \"dev server-renderer -if esm-bundler\" dev-sfc-serve", "serve": "serve", "open": "open http://localhost:3000/packages-private/template-explorer/local.html", "build-sfc-playground": "run-s build-all-cjs build-runtime-esm build-browser-esm build-ssr-esm build-sfc-playground-self", "build-all-cjs": "node scripts/build.js vue runtime compiler reactivity shared -af cjs", "build-runtime-esm": "node scripts/build.js runtime reactivity shared -af esm-bundler && node scripts/build.js vue -f esm-bundler-runtime && node scripts/build.js vue -f esm-browser-runtime", "build-browser-esm": "node scripts/build.js runtime reactivity shared -af esm-bundler && node scripts/build.js vue -f esm-bundler && node scripts/build.js vue -f esm-browser", "build-ssr-esm": "node scripts/build.js compiler-sfc server-renderer -f esm-browser", "build-sfc-playground-self": "cd packages-private/sfc-playground && npm run build", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged && pnpm check", "commit-msg": "node scripts/verify-commit.js"}, "lint-staged": {"*.{js,json}": ["prettier --write"], "*.ts?(x)": ["eslint --fix", "prettier --parser=typescript --write"]}, "engines": {"node": ">=18.12.0"}, "devDependencies": {"@babel/parser": "catalog:", "@babel/types": "catalog:", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "5.0.4", "@swc/core": "^1.11.29", "@types/hash-sum": "^1.0.2", "@types/node": "^22.15.29", "@types/semver": "^7.7.0", "@types/serve-handler": "^6.1.4", "@vitest/coverage-v8": "^3.1.4", "@vitest/eslint-plugin": "^1.2.1", "@vue/consolidate": "1.0.0", "conventional-changelog-cli": "^5.0.0", "enquirer": "^2.4.1", "esbuild": "^0.25.5", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^9.27.0", "eslint-plugin-import-x": "^4.13.1", "estree-walker": "catalog:", "jsdom": "^26.1.0", "lint-staged": "^16.0.0", "lodash": "^4.17.21", "magic-string": "^0.30.17", "markdown-table": "^3.0.4", "marked": "13.0.3", "npm-run-all2": "^7.0.2", "picocolors": "^1.1.1", "prettier": "^3.5.3", "pretty-bytes": "^6.1.1", "pug": "^3.0.3", "puppeteer": "~24.9.0", "rimraf": "^6.0.1", "rollup": "^4.41.1", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-esbuild": "^6.2.1", "rollup-plugin-polyfill-node": "^0.13.0", "semver": "^7.7.2", "serve": "^14.2.4", "serve-handler": "^6.1.6", "simple-git-hooks": "^2.13.0", "todomvc-app-css": "^2.4.3", "tslib": "^2.8.1", "typescript": "~5.6.2", "typescript-eslint": "^8.32.1", "vite": "catalog:", "vitest": "^3.1.4"}}