// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`defineProps > basic usage 1`] = `
"const bar = 1

export default {
  props: {
  foo: String
},
  setup(__props, { expose: __expose }) {
  __expose();

const props = __props

return { props, bar }
}

}"
`;

exports[`defineProps > custom element retains the props type & default value & production mode 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
interface Props { 
          foo?: number;
      }
      
export default /*@__PURE__*/_defineComponent({
  __name: 'app.ce',
  props: {
    foo: { default: 5.5, type: Number }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      const props = __props;
      
return { props }
}

})"
`;

exports[`defineProps > custom element retains the props type & production mode 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  __name: 'app.ce',
  props: {
    foo: {type: Number}
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      const props = __props
      
return { props }
}

})"
`;

exports[`defineProps > defineProps w/ runtime options 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  props: { foo: String },
  setup(__props, { expose: __expose }) {
  __expose();

const props = __props

return { props }
}

})"
`;

exports[`defineProps > destructure without enabling reactive destructure 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  props: {
    foo: { type: null, required: true }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      const { foo } = __props
      
return { foo }
}

})"
`;

exports[`defineProps > should escape names w/ special symbols 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  props: {
    "spa ce": { type: null, required: true },
    "exclamation!mark": { type: null, required: true },
    "double\\"quote": { type: null, required: true },
    "hash#tag": { type: null, required: true },
    "dollar$sign": { type: null, required: true },
    "percentage%sign": { type: null, required: true },
    "amper&sand": { type: null, required: true },
    "single'quote": { type: null, required: true },
    "round(brack)ets": { type: null, required: true },
    "aste*risk": { type: null, required: true },
    "pl+us": { type: null, required: true },
    "com,ma": { type: null, required: true },
    "do.t": { type: null, required: true },
    "sla/sh": { type: null, required: true },
    "co:lon": { type: null, required: true },
    "semi;colon": { type: null, required: true },
    "angle<brack>ets": { type: null, required: true },
    "equal=sign": { type: null, required: true },
    "question?mark": { type: null, required: true },
    "at@sign": { type: null, required: true },
    "square[brack]ets": { type: null, required: true },
    "back\\\\slash": { type: null, required: true },
    "ca^ret": { type: null, required: true },
    "back\`tick": { type: null, required: true },
    "curly{bra}ces": { type: null, required: true },
    "pi|pe": { type: null, required: true },
    "til~de": { type: null, required: true },
    "da-sh": { type: null, required: true }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    
    
return {  }
}

})"
`;

exports[`defineProps > w/ TS assertion 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  props: ['foo'],
  setup(__props, { expose: __expose }) {
  __expose();

      
    
return {  }
}

})"
`;

exports[`defineProps > w/ TSTypeAliasDeclaration 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type FunFoo<O> = (item: O) => boolean;
    type FunBar = FunFoo<number>;
    
export default /*@__PURE__*/_defineComponent({
  props: {
    foo: { type: Function, required: false, default: () => true },
    bar: { type: Function, required: false, default: () => true }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    
    
return {  }
}

})"
`;

exports[`defineProps > w/ exported interface 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
export interface Props { x?: number }
    
export default /*@__PURE__*/_defineComponent({
  props: {
    x: { type: Number, required: false }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    
    
return {  }
}

})"
`;

exports[`defineProps > w/ exported interface in normal script 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

      export interface Props { x?: number }
    
export default /*@__PURE__*/_defineComponent({
  props: {
    x: { type: Number, required: false }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      
    
return {  }
}

})"
`;

exports[`defineProps > w/ exported type alias 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
export type Props = { x?: number }
    
export default /*@__PURE__*/_defineComponent({
  props: {
    x: { type: Number, required: false }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    
    
return {  }
}

})"
`;

exports[`defineProps > w/ extends interface 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
interface Bar extends Foo { y?: number }
      interface Props extends Bar {
        z: number
        y: string
      }
      
      interface Foo { x?: number }
    
export default /*@__PURE__*/_defineComponent({
  props: {
    z: { type: Number, required: true },
    y: { type: String, required: true },
    x: { type: Number, required: false }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      
    
return {  }
}

})"
`;

exports[`defineProps > w/ extends intersection type 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type Foo = {
        x?: number;
      };
      interface Props extends Foo {
        z: number
        y: string
      }
      
export default /*@__PURE__*/_defineComponent({
  props: {
    z: { type: Number, required: true },
    y: { type: String, required: true },
    x: { type: Number, required: false }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      
    
return {  }
}

})"
`;

exports[`defineProps > w/ external definition 1`] = `
"import { propsModel } from './props'
    
export default {
  props: propsModel,
  setup(__props, { expose: __expose }) {
  __expose();

    const props = __props
    
return { props, get propsModel() { return propsModel } }
}

}"
`;

exports[`defineProps > w/ interface 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
interface Props { x?: number }
    
export default /*@__PURE__*/_defineComponent({
  props: {
    x: { type: Number, required: false }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    
    
return {  }
}

})"
`;

exports[`defineProps > w/ intersection type 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type Foo = {
        x?: number;
      };
      type Bar = {
        y: string;
      };
      
export default /*@__PURE__*/_defineComponent({
  props: {
    x: { type: Number, required: false },
    y: { type: String, required: true }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      
    
return {  }
}

})"
`;

exports[`defineProps > w/ leading code 1`] = `
"import { x } from './x'
    
export default {
  props: {},
  setup(__props, { expose: __expose }) {
  __expose();
const props = __props
    
return { props, get x() { return x } }
}

}"
`;

exports[`defineProps > w/ type 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
interface Test {}

    type Alias = number[]

    
export default /*@__PURE__*/_defineComponent({
  props: {
    string: { type: String, required: true },
    number: { type: Number, required: true },
    boolean: { type: Boolean, required: true },
    object: { type: Object, required: true },
    objectLiteral: { type: Object, required: true },
    fn: { type: Function, required: true },
    functionRef: { type: Function, required: true },
    objectRef: { type: Object, required: true },
    dateTime: { type: Date, required: true },
    array: { type: Array, required: true },
    arrayRef: { type: Array, required: true },
    tuple: { type: Array, required: true },
    set: { type: Set, required: true },
    literal: { type: String, required: true },
    optional: { type: null, required: false },
    recordRef: { type: Object, required: true },
    interface: { type: Object, required: true },
    alias: { type: Array, required: true },
    method: { type: Function, required: true },
    symbol: { type: Symbol, required: true },
    error: { type: Error, required: true },
    extract: { type: Number, required: true },
    exclude: { type: [Number, Boolean], required: true },
    uppercase: { type: String, required: true },
    params: { type: Array, required: true },
    nonNull: { type: String, required: true },
    objectOrFn: { type: [Function, Object], required: true },
    union: { type: [String, Number], required: true },
    literalUnion: { type: String, required: true },
    literalUnionNumber: { type: Number, required: true },
    literalUnionMixed: { type: [String, Number, Boolean], required: true },
    intersection: { type: Object, required: true },
    intersection2: { type: String, required: true },
    foo: { type: [Function, null], required: true },
    unknown: { type: null, required: true },
    unknownUnion: { type: null, required: true },
    unknownIntersection: { type: Object, required: true },
    unknownUnionWithBoolean: { type: Boolean, required: true, skipCheck: true },
    unknownUnionWithFunction: { type: Function, required: true, skipCheck: true }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    
    
return {  }
}

})"
`;

exports[`defineProps > w/ type alias 1`] = `
"import { defineComponent as _defineComponent } from 'vue'
type Props = { x?: number }
    
export default /*@__PURE__*/_defineComponent({
  props: {
    x: { type: Number, required: false }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    
    
return {  }
}

})"
`;

exports[`defineProps > withDefaults (dynamic) 1`] = `
"import { mergeDefaults as _mergeDefaults, defineComponent as _defineComponent } from 'vue'
import { defaults } from './foo'
    
export default /*@__PURE__*/_defineComponent({
  props: /*@__PURE__*/_mergeDefaults({
    foo: { type: String, required: false },
    bar: { type: Number, required: false },
    baz: { type: Boolean, required: true }
  }, { ...defaults }),
  setup(__props: any, { expose: __expose }) {
  __expose();

    const props = __props
    
return { props, get defaults() { return defaults } }
}

})"
`;

exports[`defineProps > withDefaults (dynamic) w/ production mode 1`] = `
"import { mergeDefaults as _mergeDefaults, defineComponent as _defineComponent } from 'vue'
import { defaults } from './foo'
    
export default /*@__PURE__*/_defineComponent({
  props: /*@__PURE__*/_mergeDefaults({
    foo: { type: Function },
    bar: { type: Boolean },
    baz: { type: [Boolean, Function] },
    qux: {}
  }, { ...defaults }),
  setup(__props: any, { expose: __expose }) {
  __expose();

    const props = __props
    
return { props, get defaults() { return defaults } }
}

})"
`;

exports[`defineProps > withDefaults (reference) 1`] = `
"import { mergeDefaults as _mergeDefaults, defineComponent as _defineComponent } from 'vue'
import { defaults } from './foo'
    
export default /*@__PURE__*/_defineComponent({
  props: /*@__PURE__*/_mergeDefaults({
    foo: { type: String, required: false },
    bar: { type: Number, required: false },
    baz: { type: Boolean, required: true }
  }, defaults),
  setup(__props: any, { expose: __expose }) {
  __expose();

    const props = __props
    
return { props, get defaults() { return defaults } }
}

})"
`;

exports[`defineProps > withDefaults (static) + normal script 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

      interface Props {
        a?: string;
      }
    
export default /*@__PURE__*/_defineComponent({
  props: {
    a: { type: String, required: false, default: "a" }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

      const props = __props;
    
return { props }
}

})"
`;

exports[`defineProps > withDefaults (static) 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  props: {
    foo: { type: String, required: false, default: 'hi' },
    bar: { type: Number, required: false },
    baz: { type: Boolean, required: true },
    qux: { type: Function, required: false, default() { return 1 } },
    quux: { type: Function, required: false, default() { } },
    quuxx: { type: Promise, required: false, async default() { return await Promise.resolve('hi') } },
    fred: { type: String, required: false, get default() { return 'fred' } }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    const props = __props
    
return { props }
}

})"
`;

exports[`defineProps > withDefaults (static) w/ production mode 1`] = `
"import { defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  props: {
    foo: {},
    bar: { type: Boolean },
    baz: { type: [Boolean, Function], default: true },
    qux: { default: 'hi' }
  },
  setup(__props: any, { expose: __expose }) {
  __expose();

    const props = __props
    
return { props }
}

})"
`;

exports[`defineProps > withDefaults w/ dynamic object method 1`] = `
"import { mergeDefaults as _mergeDefaults, defineComponent as _defineComponent } from 'vue'

export default /*@__PURE__*/_defineComponent({
  props: /*@__PURE__*/_mergeDefaults({
    foo: { type: Function, required: false }
  }, {
      ['fo' + 'o']() { return 'foo' }
    }),
  setup(__props: any, { expose: __expose }) {
  __expose();

    const props = __props
    
return { props }
}

})"
`;
