const { parse } = require('@vue/compiler-sfc')

// 测试用的 Vue 单文件组件
const source = `
<template>
  <div class="example">
    {{ message }}
    <button @click="increment">Count: {{ count }}</button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      message: 'Hello',
      count: 0
    }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}
</script>

<style scoped>
.example {
  color: red;
}
</style>
`

// 添加调试点
debugger

// 解析 SFC
const { descriptor, errors } = parse(source)

// 查看解析结果
console.log('Template AST:', JSON.stringify(descriptor.template.ast, null, 2))
console.log('Script:', JSON.stringify(descriptor.script, null, 2))
console.log('Styles:', JSON.stringify(descriptor.styles, null, 2))

// 查看具体的 template AST 节点
debugger
const templateAst = descriptor.template.ast
console.log('Root node type:', templateAst.type)
console.log('First element:', templateAst.children[0])
