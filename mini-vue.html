<!-- <!doctype html>
<html>
  <head>
    <title>Mini Vue</title>
  </head>
  <style>
    .grid-container {
      display: grid;
      grid-template-columns: 200px 1fr 200px;
      height: 100vh;
    }

    .left {
      background-color: #eee;
    }

    .center {
      background-color: #cceeff;
    }

    .right {
      background-color: #eee;
    }
  </style>
  <body>
    <!-- 使用方式类似 Vue -->
    <div id="app">
      <h1>{{ title }}</h1>
      <input v-model="message" />
      <p>{{ message }}</p>
      <button @click="increment">点击次数: {{ count }}</button>
    </div>
    <div class="grid-container">
      <div class="left">左侧栏</div>
      <div class="center">中间内容</div>
      <div class="right">右侧栏</div>
    </div>
    <script>
      class MiniVue {
        constructor(options) {
          console.log(options, 'options')
          this.$data = options.data()
          this.$el = document.querySelector(options.el)
          this.methods = options.methods

          // 初始化
          this.observe(this.$data)
          this.compile(this.$el)
        }

        // 数据响应式处理
        observe(data) {
          Object.keys(data).forEach(key => {
            let value = data[key]
            const dep = new Dep()

            Object.defineProperty(data, key, {
              get() {
                // 收集依赖
                if (Dep.target) {
                  dep.addSub(Dep.target)
                }
                return value
              },
              set(newValue) {
                if (value !== newValue) {
                  value = newValue
                  // 通知更新
                  dep.notify()
                }
              },
            })
          })
        }

        // 编译模板
        compile(el) {
          // console.log(el, 'el')

          const nodes = el.childNodes
          nodes.forEach(node => {
            // 文本节点，处理 {{ }} 插值
            if (node.nodeType === 3) {
              const reg = /\{\{(.*?)\}\}/g
              const text = node.textContent
              if (reg.test(text)) {
                const replacedText = text.replace(reg, (match, key) => {
                  // 创建一个 Watcher
                  new Watcher(this.$data, key.trim(), newValue => {
                    node.textContent = text.replace(reg, (match, key) => {
                      return this.$data[key.trim()]
                    })
                  })
                  return this.$data[key.trim()]
                })
                node.textContent = replacedText
              }
            }

            // 元素节点，处理指令和事件
            if (node.nodeType === 1) {
              // 处理 v-model
              if (node.hasAttribute('v-model')) {
                const key = node.getAttribute('v-model')
                node.value = this.$data[key]

                new Watcher(this.$data, key, newValue => {
                  node.value = newValue
                })

                node.addEventListener('input', e => {
                  this.$data[key] = e.target.value
                })
              }

              // 处理 @click
              const clickAttr = Array.from(node.attributes).find(attr =>
                attr.name.startsWith('@click'),
              )
              if (clickAttr) {
                const methodName = clickAttr.value
                node.addEventListener('click', e => {
                  this.methods[methodName].call(this.$data)
                })
              }

              // 递归处理子节点
              if (node.childNodes.length) {
                this.compile(node)
              }
            }
          })
        }
      }

      // 依赖收集器
      class Dep {
        constructor() {
          this.subs = []
        }

        addSub(watcher) {
          this.subs.push(watcher)
        }

        notify() {
          this.subs.forEach(watcher => watcher.update())
        }
      }

      // 观察者
      class Watcher {
        constructor(data, key, callback) {
          this.data = data
          this.key = key
          this.callback = callback
          this.value = this.get()
        }

        get() {
          Dep.target = this
          const value = this.data[this.key]
          Dep.target = null
          return value
        }

        update() {
          const newValue = this.data[this.key]
          if (this.value !== newValue) {
            this.value = newValue
            this.callback(newValue)
          }
        }
      }

      // 创建实例
      const app = new MiniVue({
        el: '#app',
        data() {
          return {
            title: 'Mini Vue 示例',
            message: 'Hello World',
            count: 0,
          }
        },
        methods: {
          increment() {
            this.count++
          },
        },
      })

      // 为了调试方便，暴露到全局
      window.app = app
    </script>
  </body>
</html> -->
