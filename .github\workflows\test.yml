name: 'test'

on: workflow_call

permissions:
  contents: read # to fetch code (actions/checkout)

jobs:
  unit-test:
    runs-on: ubuntu-latest
    env:
      PUPPETEER_SKIP_DOWNLOAD: 'true'
    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'pnpm'

      - run: pnpm install

      - name: Run unit tests
        run: pnpm run test-unit

  unit-test-windows:
    runs-on: windows-latest
    env:
      PUPPETEER_SKIP_DOWNLOAD: 'true'
    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'pnpm'

      - run: pnpm install

      - name: Run compiler unit tests
        run: pnpm run test-unit compiler

      - name: Run ssr unit tests
        run: pnpm run test-unit server-renderer

  e2e-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup cache for Chromium binary
        uses: actions/cache@v4
        with:
          path: ~/.cache/puppeteer
          key: chromium-${{ hashFiles('pnpm-lock.yaml') }}

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'pnpm'

      - run: pnpm install
      - run: node node_modules/puppeteer/install.mjs

      - name: Run e2e tests
        run: pnpm run test-e2e

      - name: verify treeshaking
        run: node scripts/verify-treeshaking.js

  lint-and-test-dts:
    runs-on: ubuntu-latest
    env:
      PUPPETEER_SKIP_DOWNLOAD: 'true'
    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'pnpm'

      - run: pnpm install

      - name: Run eslint
        run: pnpm run lint

      - name: Run prettier
        run: pnpm run format-check

      - name: Run tsc
        run: pnpm run check

      - name: Run type declaration tests
        run: pnpm run test-dts
