{
  // Use the project's typescript version
  "typescript.tsdk": "node_modules/typescript/lib",

  "cSpell.enabledLanguageIds": ["markdown", "plaintext", "text", "yml"],

  // Use prettier to format typescript, javascript and JSON files
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.formatOnSave": true,
  "liveServer.settings.port": 5502,
  "workbench.editor.splitInGroupLayout": "vertical"
}
