name: size report

on:
  workflow_run:
    workflows: ['size data']
    types:
      - completed

permissions:
  contents: read
  pull-requests: write
  issues: write

env:
  PUPPETEER_SKIP_DOWNLOAD: 'true'

jobs:
  size-report:
    runs-on: ubuntu-latest
    if: >
      github.repository == 'vuejs/core' &&
      github.event.workflow_run.event == 'pull_request' &&
      github.event.workflow_run.conclusion == 'success'
    steps:
      - uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4.1.0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Download Size Data
        uses: dawidd6/action-download-artifact@v9
        with:
          name: size-data
          run_id: ${{ github.event.workflow_run.id }}
          path: temp/size

      - name: Read PR Number
        id: pr-number
        uses: juliangruber/read-file-action@v1
        with:
          path: temp/size/number.txt

      - name: Read base branch
        id: pr-base
        uses: juliangruber/read-file-action@v1
        with:
          path: temp/size/base.txt

      - name: Download Previous Size Data
        uses: dawidd6/action-download-artifact@v9
        with:
          branch: ${{ steps.pr-base.outputs.content }}
          workflow: size-data.yml
          event: push
          name: size-data
          path: temp/size-prev
          if_no_artifact_found: warn

      - name: Prepare report
        run: node scripts/size-report.js > size-report.md

      - name: Read Size Report
        id: size-report
        uses: juliangruber/read-file-action@v1
        with:
          path: ./size-report.md

      - name: Create Comment
        uses: actions-cool/maintain-one-comment@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          number: ${{ steps.pr-number.outputs.content }}
          body: |
            ${{ steps.size-report.outputs.content }}
            <!-- VUE_CORE_SIZE -->
          body-include: '<!-- VUE_CORE_SIZE -->'
