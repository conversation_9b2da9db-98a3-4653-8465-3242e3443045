{"extends": "./tsconfig.json", "compilerOptions": {"types": ["node"], "declaration": true, "emitDeclarationOnly": true, "stripInternal": true, "composite": false}, "include": ["packages/global.d.ts", "packages/vue/src", "packages/vue-compat/src", "packages/compiler-core/src", "packages/compiler-dom/src", "packages/runtime-core/src", "packages/runtime-dom/src", "packages/reactivity/src", "packages/shared/src", "packages/compiler-sfc/src", "packages/compiler-ssr/src", "packages/server-renderer/src"]}