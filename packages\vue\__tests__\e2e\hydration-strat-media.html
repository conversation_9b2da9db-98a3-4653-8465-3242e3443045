<script src="../../dist/vue.global.js"></script>

<div>resize the window width to < 500px to hydrate</div>
<div id="app"><button>0</button></div>

<script>
  window.isHydrated = false
  const {
    createSSRApp,
    defineAsyncComponent,
    h,
    ref,
    onMounted,
    hydrateOnMediaQuery,
  } = Vue

  const Comp = {
    props: {
      value: Boolean,
    },
    setup(props) {
      const count = ref(0)
      onMounted(() => {
        console.log('hydrated')
        window.isHydrated = true
      })
      return () => {
        props.value
        return h('button', { onClick: () => count.value++ }, count.value)
      }
    },
  }

  const AsyncComp = defineAsyncComponent({
    loader: () => Promise.resolve(Comp),
    hydrate: hydrateOnMediaQuery('(max-width:500px)'),
  })

  createSSRApp({
    setup() {
      onMounted(() => {
        window.isRootMounted = true
      })

      const show = (window.show = ref(true))
      return () => h(AsyncComp, { value: show.value })
    },
  }).mount('#app')
</script>
